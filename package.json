{"name": "minimalnotes", "private": true, "version": "0.1.0", "type": "module", "main": "main.cjs", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "electron": "cross-env NODE_ENV=development electron ."}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.6.1", "slate": "^0.117.0", "slate-history": "^0.113.1", "slate-react": "^0.116.0", "zustand": "^4.5.1"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/react-syntax-highlighter": "^15.5.11", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "cross-env": "^7.0.3", "electron": "^36.4.0", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.1.0"}}