import { useState, useRef, useEffect, useMemo } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Tooltip,
  Typography,
  Menu,
  MenuItem,
  ListItemText,
} from '@mui/material';
import {
  FormatBold,
  FormatItalic,
  FormatUnderlined,
  FormatListBulleted,
  FormatListNumbered,
  Image as ImageIcon,
  Code as CodeIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import { createEditor, Descendant, Transforms, Editor as SlateEditor, Element as SlateElement, BaseEditor } from 'slate';
import { Slate, Editable, withReact, ReactEditor } from 'slate-react';
import { useNotesStore } from '../store/notesStore';
import BrushIcon from '@mui/icons-material/Brush';
import DrawingBoard from './DrawingBoard';
import { withHistory } from 'slate-history';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

// Custom element types for Slate
export type CustomElement =
  | { type: 'paragraph'; children: CustomText[] }
  | { type: 'bulleted-list'; children: CustomElement[] }
  | { type: 'numbered-list'; children: CustomElement[] }
  | { type: 'list-item'; children: CustomText[] }
  | { type: 'code'; language?: string; children: CustomText[] }
  | { type: 'image'; url: string; children: CustomText[] };
export type CustomText = { text: string; bold?: boolean; italic?: boolean; underline?: boolean; code?: boolean };
declare module 'slate' {
  interface CustomTypes {
    Editor: BaseEditor & ReactEditor;
    Element: CustomElement;
    Text: CustomText;
  }
}

interface EditorProps {
  centerContent?: boolean;
}

const initialValue: Descendant[] = [
  { type: 'paragraph', children: [{ text: '' }] },
];

const LIST_TYPES = ['numbered-list', 'bulleted-list'];

type BlockType = 'paragraph' | 'bulleted-list' | 'numbered-list' | 'list-item' | 'code';

const SUPPORTED_LANGUAGES = [
  { value: 'text', label: 'Plain Text' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'cpp', label: 'C++' },
  { value: 'c', label: 'C' },
  { value: 'html', label: 'HTML' },
  { value: 'css', label: 'CSS' },
  { value: 'json', label: 'JSON' },
  { value: 'sql', label: 'SQL' },
  { value: 'bash', label: 'Bash' },
  { value: 'php', label: 'PHP' },
  { value: 'ruby', label: 'Ruby' },
  { value: 'go', label: 'Go' },
  { value: 'rust', label: 'Rust' },
  { value: 'swift', label: 'Swift' },
  { value: 'kotlin', label: 'Kotlin' },
  { value: 'xml', label: 'XML' },
  { value: 'yaml', label: 'YAML' },
];

const NoteEditor = ({ centerContent }: EditorProps) => {
  const [isPreview, setIsPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { notes, activeNoteId, updateNote, addNote } = useNotesStore();
  const activeNote = notes.find(note => note.id === activeNoteId);
  const editor = useMemo(() => withHistory(withReact(createEditor())), []);
  const [value, setValue] = useState<Descendant[]>(initialValue);
  const [showDrawing, setShowDrawing] = useState(false);
  const [languageMenuAnchor, setLanguageMenuAnchor] = useState<HTMLElement | null>(null);
  const [selectedCodeBlockPath, setSelectedCodeBlockPath] = useState<any>(null);

  useEffect(() => {
    if (activeNote) {
      setTitle(activeNote.title);
      if (activeNote.content) {
        try {
          setValue(JSON.parse(activeNote.content));
        } catch {
          setValue(initialValue);
        }
      } else {
        setValue(initialValue);
      }
    } else {
      setTitle('');
      setValue(initialValue);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeNote]);

  const [title, setTitle] = useState('');

  const handleTitleChange = (newTitle: string) => {
    setTitle(newTitle);
    if (activeNoteId) {
      updateNote(activeNoteId, { title: newTitle });
    }
  };

  const handleSlateChange = (val: Descendant[]) => {
    setValue(val);
    if (activeNoteId) {
      updateNote(activeNoteId, { content: JSON.stringify(val) });
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        const imageNode: CustomElement = {
          type: 'image',
          url: imageUrl,
          children: [{ text: '' }]
        };
        // Insert only if not currently deleting an image node
        if (!editor.selection || !SlateEditor.above(editor, { match: n => SlateElement.isElement(n) && n.type === 'image' })) {
          Transforms.insertNodes(editor, imageNode);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDrawingSave = (drawingData: any[]) => {
    if (activeNoteId) {
      updateNote(activeNoteId, { drawingData });
    }
    setShowDrawing(false);
  };

  const handleLanguageChange = (language: string) => {
    if (selectedCodeBlockPath) {
      Transforms.setNodes(
        editor,
        { language },
        { at: selectedCodeBlockPath }
      );
    }
    setLanguageMenuAnchor(null);
    setSelectedCodeBlockPath(null);
  };

  // Formatting helpers
  const toggleMark = (format: 'bold' | 'italic' | 'underline') => {
    const isActive = isMarkActive(editor, format);
    if (isActive) {
      SlateEditor.removeMark(editor, format);
    } else {
      SlateEditor.addMark(editor, format, true);
    }
  };

  const isMarkActive = (editor: SlateEditor, format: 'bold' | 'italic' | 'underline' | 'code') => {
    const marks = SlateEditor.marks(editor);
    return marks ? marks[format] === true : false;
  };

  // Block helpers
  const toggleBlock = (format: BlockType) => {
    const isActive = isBlockActive(editor, format);
    const isList = LIST_TYPES.includes(format);

    Transforms.unwrapNodes(editor, {
      match: n =>
        !SlateEditor.isEditor(n) && SlateElement.isElement(n) && LIST_TYPES.includes((n as any).type),
      split: true,
    });

    let newType: BlockType = isActive ? 'paragraph' : format;
    if (!isActive && isList) {
      Transforms.setNodes(editor, { type: 'list-item' });
      Transforms.wrapNodes(editor, { type: format, children: [] });
    } else {
      Transforms.setNodes(editor, { type: newType });
    }
  };

  const isBlockActive = (editor: SlateEditor, format: string) => {
    const [match] = SlateEditor.nodes(editor, {
      match: n =>
        !SlateEditor.isEditor(n) && SlateElement.isElement(n) && n.type === format,
    });
    return !!match;
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    // Code block shortcuts
    if ((event.ctrlKey || event.metaKey) && event.key === '`') {
      event.preventDefault();
      if (event.shiftKey) {
        // Ctrl+Shift+` - Create code block
        toggleBlock('code');
      } else {
        // Ctrl+` - Toggle inline code (we'll implement this as a mark)
        toggleMark('code' as any);
      }
      return;
    }

    // Handle Enter key in code blocks
    if (event.key === 'Enter') {
      const [codeMatch] = SlateEditor.nodes(editor, {
        match: n => !SlateEditor.isEditor(n) && SlateElement.isElement(n) && n.type === 'code',
      });

      if (codeMatch) {
        event.preventDefault();
        // Insert a new line within the code block
        SlateEditor.insertText(editor, '\n');
        return;
      }
    }

    if (event.key === 'Backspace') {
      // Handle image deletion - only delete the specific image at cursor
      const [imgMatch] = SlateEditor.nodes(editor, {
        match: n => !SlateEditor.isEditor(n) && SlateElement.isElement(n) && n.type === 'image',
      });

      if (imgMatch) {
        event.preventDefault();
        const [, path] = imgMatch;
        Transforms.removeNodes(editor, { at: path });
        return;
      }

      // Only handle backspace for empty code blocks
      const [codeMatch] = SlateEditor.nodes(editor, {
        match: n => !SlateEditor.isEditor(n) && SlateElement.isElement(n) && n.type === 'code',
      });

      if (codeMatch) {
        const [node, path] = codeMatch;
        const { selection } = editor;

        // Check if code block is empty and cursor is at start
        if (SlateEditor.isEmpty(editor, node as any) ||
            (selection && SlateEditor.isStart(editor, selection.anchor, path))) {
          event.preventDefault();
          Transforms.setNodes(editor, { type: 'paragraph' });
          return;
        }
      }
    }
    // Handle undo/redo (typed and handwritten)
    if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'z') {
      event.preventDefault();
      if (event.shiftKey) {
        if (typeof editor.redo === 'function') editor.redo();
        // If DrawingBoard open, trigger undo in drawing
        if (showDrawing && typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('drawing-undo'));
        }
      } else {
        if (typeof editor.undo === 'function') editor.undo();
        if (showDrawing && typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('drawing-undo'));
        }
      }
    }
    if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'y') {
      event.preventDefault();
      if (typeof editor.redo === 'function') editor.redo();
      if (showDrawing && typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('drawing-redo'));
      }
    }
  };

  if (notes.length === 0) {
    return (
      <Box sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        bgcolor: 'background.paper',
      }}>
        {/* Animated Welcome Text */}
        <Typography variant="h3" sx={{ fontWeight: 700, mb: 2, fontFamily: 'Inter, sans-serif', animation: 'typing 2s steps(22), blink .5s step-end infinite alternate' }}>
          <span id="welcome-typing">Welcome to Minimal Notes</span>
        </Typography>
        <style>{`
          @keyframes typing {
            from { width: 0 }
            to { width: 100% }
          }
          @keyframes blink {
            50% { border-color: transparent }
          }
          #welcome-typing {
            display: inline-block;
            overflow: hidden;
            border-right: .15em solid orange;
            white-space: nowrap;
            width: 22ch;
            animation: typing 2s steps(22), blink .5s step-end infinite alternate;
          }
        `}</style>
        <Typography variant="subtitle1" sx={{ mb: 4, color: 'text.secondary', animation: 'fadeIn 2s' }}>
          Your simple, distraction-free note app
        </Typography>
        <style>{`
          @keyframes fadeIn {
            from { opacity: 0 }
            to { opacity: 1 }
          }
        `}</style>
        <IconButton
          color="primary"
          size="large"
          sx={{ fontSize: '1.2rem', border: '1px solid', borderColor: 'primary.main', borderRadius: 2, px: 4, py: 1 }}
          onClick={() => {
            addNote({ title: 'Untitled Note', content: '' });
          }}
        >
          Create your first note
        </IconButton>
      </Box>
    );
  }

  // Graph View Modal
  const [showGraph, setShowGraph] = useState(false);
  const closeGraph = () => setShowGraph(false);

  // Export Note Functionality
  const getTextFromNode = (node: any): string => {
  if (typeof node.text === 'string') return node.text;
  if (Array.isArray(node.children)) return node.children.map(getTextFromNode).join('');
  return '';
};
const exportNote = (type: 'pdf' | 'word') => {
  const noteTitle = title || 'MinimalNotesExport';
  const noteContent = value.map(getTextFromNode).join('\n');
  if (type === 'pdf') {
    // Use jsPDF or similar (placeholder)
    const blob = new Blob([noteContent], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${noteTitle}.pdf`;
    a.click();
    URL.revokeObjectURL(url);
  } else {
    // Word export (simple .doc)
    const blob = new Blob([noteContent], { type: 'application/msword' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${noteTitle}.doc`;
    a.click();
    URL.revokeObjectURL(url);
  }
};

  return (
    <Box
      sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        p: centerContent ? 0 : 4,
        bgcolor: 'background.paper',
        alignItems: centerContent ? 'center' : 'stretch',
        justifyContent: centerContent ? 'center' : 'flex-start',
      }}
    >
      {/* Graph View Modal Placeholder */}
      {showGraph && (
        <Box sx={{ position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', bgcolor: 'rgba(0,0,0,0.6)', zIndex: 9999, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Box sx={{ bgcolor: 'background.paper', borderRadius: 4, p: 4, minWidth: 400, minHeight: 300, boxShadow: 6 }}>
            <Typography variant="h5" sx={{ mb: 2 }}>Graph View (coming soon)</Typography>
            <Typography variant="body1" sx={{ mb: 2 }}>A visual graph of your notes and their connections will appear here.</Typography>
            <IconButton onClick={closeGraph} sx={{ position: 'absolute', top: 16, right: 16 }}>
              <span style={{ fontWeight: 'bold', color: '#d9534f', fontSize: 18 }}>✖</span>
            </IconButton>
          </Box>
        </Box>
      )}
      <Box
        sx={{
          width: centerContent ? '100%' : '100%',
          maxWidth: centerContent ? 800 : 'none',
          mx: centerContent ? 'auto' : 0,
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <TextField
          value={title}
          onChange={e => handleTitleChange(e.target.value)}
          placeholder="Note Title"
          variant="standard"
          InputProps={{
            disableUnderline: true,
            style: {
              fontSize: '2.5rem',
              fontWeight: 700,
              fontFamily: 'Inter, sans-serif',
              marginBottom: '1.5rem',
            },
          }}
          sx={{ mb: 3 }}
        />
        <Box sx={{ mb: 2, display: 'flex', gap: 1 }}>
          <Tooltip title="Bold">
            <IconButton onClick={() => toggleMark('bold')} color={isMarkActive(editor, 'bold') ? 'primary' : 'default'}>
              <FormatBold />
            </IconButton>
          </Tooltip>
          <Tooltip title="Italic">
            <IconButton onClick={() => toggleMark('italic')} color={isMarkActive(editor, 'italic') ? 'primary' : 'default'}>
              <FormatItalic />
            </IconButton>
          </Tooltip>
          <Tooltip title="Underline">
            <IconButton onClick={() => toggleMark('underline')} color={isMarkActive(editor, 'underline') ? 'primary' : 'default'}>
              <FormatUnderlined />
            </IconButton>
          </Tooltip>
          <Tooltip title="Bullet List">
            <IconButton onClick={() => toggleBlock('bulleted-list')}>
              <FormatListBulleted />
            </IconButton>
          </Tooltip>
          <Tooltip title="Numbered List">
            <IconButton onClick={() => toggleBlock('numbered-list')}>
              <FormatListNumbered />
            </IconButton>
          </Tooltip>
          <Tooltip title="Code Block">
            <IconButton onClick={() => toggleBlock('code')}>
              <CodeIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Insert Image">
            <IconButton onClick={() => fileInputRef.current?.click()}>
              <ImageIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Handwrite">
            <IconButton onClick={() => setShowDrawing(true)}>
              <BrushIcon />
            </IconButton>
          </Tooltip>
          <Box sx={{ flexGrow: 1 }} />
          {/* Delete Note Button */}
          <Tooltip title="Delete Note">
            <IconButton color="error" onClick={() => {
              if (activeNoteId) {
                if (window.confirm('Delete this note?')) {
                  useNotesStore.getState().deleteNote(activeNoteId);
                }
              }
            }}>
              <span style={{ fontWeight: 'bold', color: '#d9534f', fontSize: 18 }}>🗑️</span>
            </IconButton>
          </Tooltip>
          {/* Export Note Button */}
          <Tooltip title="Export as PDF">
            <IconButton color="primary" onClick={() => exportNote('pdf')}>
              <span style={{ fontWeight: 'bold', color: '#007bff', fontSize: 18 }}>⭳ PDF</span>
            </IconButton>
          </Tooltip>
          <Tooltip title="Export as Word">
            <IconButton color="primary" onClick={() => exportNote('word')}>
              <span style={{ fontWeight: 'bold', color: '#007bff', fontSize: 18 }}>⭳ Word</span>
            </IconButton>
          </Tooltip>
          {/* Graph View Button */}
          <Tooltip title="Graph View">
            <IconButton color="secondary" onClick={() => setShowGraph(true)}>
              <span style={{ fontWeight: 'bold', color: '#6f42c1', fontSize: 18 }}>🕸️</span>
            </IconButton>
          </Tooltip>
          <Tooltip title={isPreview ? 'Edit' : 'Preview'}>
            <IconButton onClick={() => setIsPreview(!isPreview)}>
              {isPreview ? <EditIcon /> : <VisibilityIcon />}
            </IconButton>
          </Tooltip>
          <input
            type="file"
            ref={fileInputRef}
            style={{ display: 'none' }}
            accept="image/*"
            onChange={handleImageUpload}
          />
        </Box>
        <Slate editor={editor} initialValue={value} onChange={handleSlateChange}>
          <Editable
            style={{
              width: '100%',
              height: '100%',
              flexGrow: 1,
              resize: 'none',
              border: 'none',
              outline: 'none',
              background: 'transparent',
              color: 'inherit',
              fontSize: '1.1rem',
              lineHeight: 1.4,
              fontFamily: 'Inter, sans-serif',
              padding: 0,
              margin: 0,
              boxSizing: 'border-box',
            }}
            renderLeaf={props => <Leaf {...props} />}
            renderElement={props => <Element {...props} />}
            placeholder="Start writing your note..."
            spellCheck
            autoFocus
            onKeyDown={handleKeyDown}
          />
        </Slate>
        <DrawingBoard
          open={showDrawing}
          onClose={() => setShowDrawing(false)}
          drawingData={activeNote?.drawingData}
          onSave={handleDrawingSave}
        />

        {/* Language Selection Menu */}
        <Menu
          anchorEl={languageMenuAnchor}
          open={Boolean(languageMenuAnchor)}
          onClose={() => {
            setLanguageMenuAnchor(null);
            setSelectedCodeBlockPath(null);
          }}
          PaperProps={{
            style: {
              maxHeight: 300,
              width: 200,
            },
          }}
        >
          {SUPPORTED_LANGUAGES.map((lang) => (
            <MenuItem
              key={lang.value}
              onClick={() => handleLanguageChange(lang.value)}
              dense
            >
              <ListItemText primary={lang.label} />
            </MenuItem>
          ))}
        </Menu>
      </Box>
    </Box>
  );
};

const Leaf = ({ attributes, children, leaf }: any) => {
  if (leaf.bold) children = <strong>{children}</strong>;
  if (leaf.italic) children = <em>{children}</em>;
  if (leaf.underline) children = <u>{children}</u>;
  if (leaf.code) children = (
    <code style={{
      background: '#f1f3f4',
      color: '#d73a49',
      padding: '2px 4px',
      borderRadius: 3,
      fontSize: '0.9em',
      fontFamily: '"Fira Code", "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace',
    }}>
      {children}
    </code>
  );
  return <span {...attributes}>{children}</span>;
};

// Helper to extract plain text from Slate children
function getTextFromChildren(children: any): string {
  if (typeof children === 'string') return children;
  if (Array.isArray(children)) return children.map(getTextFromChildren).join('');
  if (children && typeof children === 'object' && 'props' in children) return getTextFromChildren(children.props.children);
  return '';
}

// Helper to detect programming language from code content
function detectLanguage(code: string): string {
  const trimmedCode = code.trim().toLowerCase();

  // JavaScript/TypeScript patterns
  if (trimmedCode.includes('function') || trimmedCode.includes('const ') || trimmedCode.includes('let ') ||
      trimmedCode.includes('var ') || trimmedCode.includes('=>') || trimmedCode.includes('import ') ||
      trimmedCode.includes('export ') || trimmedCode.includes('console.log')) {
    if (trimmedCode.includes('interface ') || trimmedCode.includes(': string') || trimmedCode.includes(': number')) {
      return 'typescript';
    }
    return 'javascript';
  }

  // Python patterns
  if (trimmedCode.includes('def ') || trimmedCode.includes('import ') || trimmedCode.includes('from ') ||
      trimmedCode.includes('print(') || trimmedCode.includes('if __name__')) {
    return 'python';
  }

  // Java patterns
  if (trimmedCode.includes('public class') || trimmedCode.includes('public static void main') ||
      trimmedCode.includes('system.out.println')) {
    return 'java';
  }

  // C/C++ patterns
  if (trimmedCode.includes('#include') || trimmedCode.includes('int main') || trimmedCode.includes('printf')) {
    return 'cpp';
  }

  // HTML patterns
  if (trimmedCode.includes('<html') || trimmedCode.includes('<!doctype') ||
      (trimmedCode.includes('<') && trimmedCode.includes('>'))) {
    return 'html';
  }

  // CSS patterns
  if (trimmedCode.includes('{') && trimmedCode.includes('}') &&
      (trimmedCode.includes(':') || trimmedCode.includes('px') || trimmedCode.includes('color'))) {
    return 'css';
  }

  // JSON patterns
  if ((trimmedCode.startsWith('{') && trimmedCode.endsWith('}')) ||
      (trimmedCode.startsWith('[') && trimmedCode.endsWith(']'))) {
    try {
      JSON.parse(code);
      return 'json';
    } catch {
      // Not valid JSON
    }
  }

  // SQL patterns
  if (trimmedCode.includes('select ') || trimmedCode.includes('insert ') ||
      trimmedCode.includes('update ') || trimmedCode.includes('delete ')) {
    return 'sql';
  }

  return 'text';
}

// Helper to get text content from Slate node
function getTextFromNode(node: any): string {
  if (typeof node === 'string') return node;
  if (node.text) return node.text;
  if (node.children) {
    return node.children.map(getTextFromNode).join('');
  }
  return '';
}

const Element = ({ attributes, children, element }: any) => {

  switch (element.type) {
    case 'bulleted-list':
      return <ul {...attributes} style={{ margin: 0, paddingLeft: 24 }}>{children}</ul>;
    case 'numbered-list':
      return <ol {...attributes} style={{ margin: 0, paddingLeft: 24 }}>{children}</ol>;
    case 'list-item':
      return <li {...attributes} style={{ margin: 0 }}>{children}</li>;
    case 'code':
      const codeText = getTextFromNode(element);
      const language = element.language || detectLanguage(codeText);

      return (
        <div {...attributes} style={{
          position: 'relative',
          margin: '16px 0',
          borderRadius: 12,
          overflow: 'hidden',
          border: '1px solid #333',
          background: '#1e1e1e',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          transition: 'all 0.2s ease-in-out',
        }}>
          {/* Language indicator and copy button */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '10px 16px',
            background: 'linear-gradient(135deg, #2d2d2d 0%, #252525 100%)',
            borderBottom: '1px solid #404040',
            fontSize: '0.8rem',
            color: '#aaa',
            backdropFilter: 'blur(10px)',
          }}>
            <button
              onClick={() => {
                // For now, just show the language - we'll implement selection later
                console.log('Language selection clicked');
              }}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#888',
                cursor: 'pointer',
                textTransform: 'uppercase',
                fontWeight: 500,
                fontSize: '0.8rem',
                padding: '2px 4px',
                borderRadius: 3,
                transition: 'all 0.2s',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = '#333';
                e.currentTarget.style.color = '#fff';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'transparent';
                e.currentTarget.style.color = '#888';
              }}
            >
              {language === 'text' ? 'CODE' : language}
            </button>
            <button
              onClick={(e) => {
                navigator.clipboard.writeText(codeText);
                // Show a brief success indicator
                const button = e.currentTarget as HTMLButtonElement;
                const originalText = button.textContent;
                button.textContent = '✓ Copied';
                button.style.color = '#4ade80';
                setTimeout(() => {
                  button.textContent = originalText;
                  button.style.color = '#888';
                }, 1000);
              }}
              style={{
                background: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid #555',
                color: '#888',
                padding: '6px 12px',
                borderRadius: 6,
                fontSize: '0.75rem',
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out',
                fontWeight: 500,
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.color = '#fff';
                e.currentTarget.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';
                e.currentTarget.style.color = '#888';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              Copy
            </button>
          </div>

          {/* Code content with syntax highlighting */}
          <div style={{ position: 'relative' }}>
            <SyntaxHighlighter
              language={language === 'text' ? 'plaintext' : language}
              style={vscDarkPlus}
              customStyle={{
                margin: 0,
                padding: '20px',
                background: 'transparent',
                fontSize: '0.9rem',
                lineHeight: 1.6,
                fontFamily: '"Fira Code", "JetBrains Mono", "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace',
                fontWeight: 400,
                letterSpacing: '0.025em',
              }}
              showLineNumbers={codeText.split('\n').length > 1}
              lineNumberStyle={{
                color: '#666',
                fontSize: '0.8rem',
                paddingRight: '20px',
                userSelect: 'none',
                fontWeight: 400,
                opacity: 0.7,
              }}
              wrapLines={true}
              wrapLongLines={true}
            >
              {codeText || ' '}
            </SyntaxHighlighter>

            {/* Invisible overlay for Slate editing */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              color: 'transparent',
              background: 'transparent',
              padding: '20px',
              fontSize: '0.9rem',
              lineHeight: 1.6,
              fontFamily: '"Fira Code", "JetBrains Mono", "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace',
              fontWeight: 400,
              letterSpacing: '0.025em',
              whiteSpace: 'pre-wrap',
              overflow: 'hidden',
              pointerEvents: 'auto',
              caretColor: '#4ade80',
            }}>
              {children}
            </div>
          </div>
        </div>
      );
    case 'image':
      return (
        <div {...attributes} style={{ margin: '8px 0' }}>
          <img 
            src={element.url} 
            alt="" 
            style={{ 
              maxWidth: '100%', 
              height: 'auto',
              borderRadius: 4
            }} 
          />
          {children}
        </div>
      );
    default:
      return <p {...attributes} style={{ margin: '8px 0', lineHeight: 1.6 }}>{children}</p>;
  }
};

export default NoteEditor;
