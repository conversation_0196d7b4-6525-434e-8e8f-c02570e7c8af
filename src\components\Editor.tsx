import { useState, useRef, useEffect, useMemo } from 'react';
import {
  Box,
  <PERSON>Field,
  IconButton,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  FormatBold,
  FormatItalic,
  FormatUnderlined,
  FormatListBulleted,
  FormatListNumbered,
  Image as ImageIcon,
  Code as CodeIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import { createEditor, Descendant, Transforms, Editor as SlateEditor, Element as SlateElement, BaseEditor } from 'slate';
import { Slate, Editable, withReact, ReactEditor } from 'slate-react';
import { useNotesStore } from '../store/notesStore';
import BrushIcon from '@mui/icons-material/Brush';
import DrawingBoard from './DrawingBoard';
import { withHistory } from 'slate-history';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

// Custom element types for Slate
export type CustomElement =
  | { type: 'paragraph'; children: CustomText[] }
  | { type: 'bulleted-list'; children: CustomElement[] }
  | { type: 'numbered-list'; children: CustomElement[] }
  | { type: 'list-item'; children: CustomText[] }
  | { type: 'code'; children: CustomText[] }
  | { type: 'image'; url: string; children: CustomText[] };
export type CustomText = { text: string; bold?: boolean; italic?: boolean; underline?: boolean };
declare module 'slate' {
  interface CustomTypes {
    Editor: BaseEditor & ReactEditor;
    Element: CustomElement;
    Text: CustomText;
  }
}

interface EditorProps {
  centerContent?: boolean;
}

const initialValue: Descendant[] = [
  { type: 'paragraph', children: [{ text: '' }] },
];

const LIST_TYPES = ['numbered-list', 'bulleted-list'];

type BlockType = 'paragraph' | 'bulleted-list' | 'numbered-list' | 'list-item' | 'code';

const NoteEditor = ({ centerContent }: EditorProps) => {
  const [isPreview, setIsPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { notes, activeNoteId, updateNote, addNote } = useNotesStore();
  const activeNote = notes.find(note => note.id === activeNoteId);
  const editor = useMemo(() => withHistory(withReact(createEditor())), []);
  const [value, setValue] = useState<Descendant[]>(initialValue);
  const [showDrawing, setShowDrawing] = useState(false);

  useEffect(() => {
    if (activeNote) {
      setTitle(activeNote.title);
      if (activeNote.content) {
        try {
          setValue(JSON.parse(activeNote.content));
        } catch {
          setValue(initialValue);
        }
      } else {
        setValue(initialValue);
      }
    } else {
      setTitle('');
      setValue(initialValue);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeNote]);

  const [title, setTitle] = useState('');

  const handleTitleChange = (newTitle: string) => {
    setTitle(newTitle);
    if (activeNoteId) {
      updateNote(activeNoteId, { title: newTitle });
    }
  };

  const handleSlateChange = (val: Descendant[]) => {
    setValue(val);
    if (activeNoteId) {
      updateNote(activeNoteId, { content: JSON.stringify(val) });
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        const imageNode: CustomElement = {
          type: 'image',
          url: imageUrl,
          children: [{ text: '' }]
        };
        // Insert only if not currently deleting an image node
        if (!editor.selection || !SlateEditor.above(editor, { match: n => SlateElement.isElement(n) && n.type === 'image' })) {
          Transforms.insertNodes(editor, imageNode);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDrawingSave = (drawingData: any[]) => {
    if (activeNoteId) {
      updateNote(activeNoteId, { drawingData });
    }
    setShowDrawing(false);
  };

  // Formatting helpers
  const toggleMark = (format: 'bold' | 'italic' | 'underline') => {
    const isActive = isMarkActive(editor, format);
    if (isActive) {
      SlateEditor.removeMark(editor, format);
    } else {
      SlateEditor.addMark(editor, format, true);
    }
  };

  const isMarkActive = (editor: SlateEditor, format: 'bold' | 'italic' | 'underline') => {
    const marks = SlateEditor.marks(editor);
    return marks ? marks[format] === true : false;
  };

  // Block helpers
  const toggleBlock = (format: BlockType) => {
    const isActive = isBlockActive(editor, format);
    const isList = LIST_TYPES.includes(format);

    Transforms.unwrapNodes(editor, {
      match: n =>
        !SlateEditor.isEditor(n) && SlateElement.isElement(n) && LIST_TYPES.includes((n as any).type),
      split: true,
    });

    let newType: BlockType = isActive ? 'paragraph' : format;
    if (!isActive && isList) {
      Transforms.setNodes(editor, { type: 'list-item' });
      Transforms.wrapNodes(editor, { type: format, children: [] });
    } else {
      Transforms.setNodes(editor, { type: newType });
    }
  };

  const isBlockActive = (editor: SlateEditor, format: string) => {
    const [match] = SlateEditor.nodes(editor, {
      match: n =>
        !SlateEditor.isEditor(n) && SlateElement.isElement(n) && n.type === format,
    });
    return !!match;
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
  if (event.key === 'Backspace') {
    // Only handle backspace for empty code blocks
    const [codeMatch] = SlateEditor.nodes(editor, {
      match: n => !SlateEditor.isEditor(n) && SlateElement.isElement(n) && n.type === 'code',
    });
    
    if (codeMatch) {
      const [node, path] = codeMatch;
      const { selection } = editor;
      
      // Check if code block is empty and cursor is at start
      if (SlateEditor.isEmpty(editor, node as any) || 
          (selection && SlateEditor.isStart(editor, selection.anchor, path))) {
        event.preventDefault();
        Transforms.setNodes(editor, { type: 'paragraph' });
        return;
      }
    }
    
    // Handle image deletion
    const [imgMatch] = SlateEditor.nodes(editor, {
      match: n => !SlateEditor.isEditor(n) && SlateElement.isElement(n) && n.type === 'image',
    });
    
    if (imgMatch) {
      event.preventDefault();
      Transforms.removeNodes(editor, {
        match: n => SlateElement.isElement(n) && n.type === 'image',
      });
    }
  }
    // Handle undo/redo (typed and handwritten)
    if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'z') {
      event.preventDefault();
      if (event.shiftKey) {
        if (typeof editor.redo === 'function') editor.redo();
        // If DrawingBoard open, trigger undo in drawing
        if (showDrawing && typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('drawing-undo'));
        }
      } else {
        if (typeof editor.undo === 'function') editor.undo();
        if (showDrawing && typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('drawing-undo'));
        }
      }
    }
    if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'y') {
      event.preventDefault();
      if (typeof editor.redo === 'function') editor.redo();
      if (showDrawing && typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('drawing-redo'));
      }
    }
  };

  if (notes.length === 0) {
    return (
      <Box sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        bgcolor: 'background.paper',
      }}>
        {/* Animated Welcome Text */}
        <Typography variant="h3" sx={{ fontWeight: 700, mb: 2, fontFamily: 'Inter, sans-serif', animation: 'typing 2s steps(22), blink .5s step-end infinite alternate' }}>
          <span id="welcome-typing">Welcome to Minimal Notes</span>
        </Typography>
        <style>{`
          @keyframes typing {
            from { width: 0 }
            to { width: 100% }
          }
          @keyframes blink {
            50% { border-color: transparent }
          }
          #welcome-typing {
            display: inline-block;
            overflow: hidden;
            border-right: .15em solid orange;
            white-space: nowrap;
            width: 22ch;
            animation: typing 2s steps(22), blink .5s step-end infinite alternate;
          }
        `}</style>
        <Typography variant="subtitle1" sx={{ mb: 4, color: 'text.secondary', animation: 'fadeIn 2s' }}>
          Your simple, distraction-free note app
        </Typography>
        <style>{`
          @keyframes fadeIn {
            from { opacity: 0 }
            to { opacity: 1 }
          }
        `}</style>
        <IconButton
          color="primary"
          size="large"
          sx={{ fontSize: '1.2rem', border: '1px solid', borderColor: 'primary.main', borderRadius: 2, px: 4, py: 1 }}
          onClick={() => {
            addNote({ title: 'Untitled Note', content: '' });
          }}
        >
          Create your first note
        </IconButton>
      </Box>
    );
  }

  // Graph View Modal
  const [showGraph, setShowGraph] = useState(false);
  const closeGraph = () => setShowGraph(false);

  // Export Note Functionality
  const getTextFromNode = (node: any): string => {
  if (typeof node.text === 'string') return node.text;
  if (Array.isArray(node.children)) return node.children.map(getTextFromNode).join('');
  return '';
};
const exportNote = (type: 'pdf' | 'word') => {
  const noteTitle = title || 'MinimalNotesExport';
  const noteContent = value.map(getTextFromNode).join('\n');
  if (type === 'pdf') {
    // Use jsPDF or similar (placeholder)
    const blob = new Blob([noteContent], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${noteTitle}.pdf`;
    a.click();
    URL.revokeObjectURL(url);
  } else {
    // Word export (simple .doc)
    const blob = new Blob([noteContent], { type: 'application/msword' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${noteTitle}.doc`;
    a.click();
    URL.revokeObjectURL(url);
  }
};

  return (
    <Box
      sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        p: centerContent ? 0 : 4,
        bgcolor: 'background.paper',
        alignItems: centerContent ? 'center' : 'stretch',
        justifyContent: centerContent ? 'center' : 'flex-start',
      }}
    >
      {/* Graph View Modal Placeholder */}
      {showGraph && (
        <Box sx={{ position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', bgcolor: 'rgba(0,0,0,0.6)', zIndex: 9999, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Box sx={{ bgcolor: 'background.paper', borderRadius: 4, p: 4, minWidth: 400, minHeight: 300, boxShadow: 6 }}>
            <Typography variant="h5" sx={{ mb: 2 }}>Graph View (coming soon)</Typography>
            <Typography variant="body1" sx={{ mb: 2 }}>A visual graph of your notes and their connections will appear here.</Typography>
            <IconButton onClick={closeGraph} sx={{ position: 'absolute', top: 16, right: 16 }}>
              <span style={{ fontWeight: 'bold', color: '#d9534f', fontSize: 18 }}>✖</span>
            </IconButton>
          </Box>
        </Box>
      )}
      <Box
        sx={{
          width: centerContent ? '100%' : '100%',
          maxWidth: centerContent ? 800 : 'none',
          mx: centerContent ? 'auto' : 0,
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <TextField
          value={title}
          onChange={e => handleTitleChange(e.target.value)}
          placeholder="Note Title"
          variant="standard"
          InputProps={{
            disableUnderline: true,
            style: {
              fontSize: '2.5rem',
              fontWeight: 700,
              fontFamily: 'Inter, sans-serif',
              marginBottom: '1.5rem',
            },
          }}
          sx={{ mb: 3 }}
        />
        <Box sx={{ mb: 2, display: 'flex', gap: 1 }}>
          <Tooltip title="Bold">
            <IconButton onClick={() => toggleMark('bold')} color={isMarkActive(editor, 'bold') ? 'primary' : 'default'}>
              <FormatBold />
            </IconButton>
          </Tooltip>
          <Tooltip title="Italic">
            <IconButton onClick={() => toggleMark('italic')} color={isMarkActive(editor, 'italic') ? 'primary' : 'default'}>
              <FormatItalic />
            </IconButton>
          </Tooltip>
          <Tooltip title="Underline">
            <IconButton onClick={() => toggleMark('underline')} color={isMarkActive(editor, 'underline') ? 'primary' : 'default'}>
              <FormatUnderlined />
            </IconButton>
          </Tooltip>
          <Tooltip title="Bullet List">
            <IconButton onClick={() => toggleBlock('bulleted-list')}>
              <FormatListBulleted />
            </IconButton>
          </Tooltip>
          <Tooltip title="Numbered List">
            <IconButton onClick={() => toggleBlock('numbered-list')}>
              <FormatListNumbered />
            </IconButton>
          </Tooltip>
          <Tooltip title="Code Block">
            <IconButton onClick={() => toggleBlock('code')}>
              <CodeIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Insert Image">
            <IconButton onClick={() => fileInputRef.current?.click()}>
              <ImageIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Handwrite">
            <IconButton onClick={() => setShowDrawing(true)}>
              <BrushIcon />
            </IconButton>
          </Tooltip>
          <Box sx={{ flexGrow: 1 }} />
          {/* Delete Note Button */}
          <Tooltip title="Delete Note">
            <IconButton color="error" onClick={() => {
              if (activeNoteId) {
                if (window.confirm('Delete this note?')) {
                  useNotesStore.getState().deleteNote(activeNoteId);
                }
              }
            }}>
              <span style={{ fontWeight: 'bold', color: '#d9534f', fontSize: 18 }}>🗑️</span>
            </IconButton>
          </Tooltip>
          {/* Export Note Button */}
          <Tooltip title="Export as PDF">
            <IconButton color="primary" onClick={() => exportNote('pdf')}>
              <span style={{ fontWeight: 'bold', color: '#007bff', fontSize: 18 }}>⭳ PDF</span>
            </IconButton>
          </Tooltip>
          <Tooltip title="Export as Word">
            <IconButton color="primary" onClick={() => exportNote('word')}>
              <span style={{ fontWeight: 'bold', color: '#007bff', fontSize: 18 }}>⭳ Word</span>
            </IconButton>
          </Tooltip>
          {/* Graph View Button */}
          <Tooltip title="Graph View">
            <IconButton color="secondary" onClick={() => setShowGraph(true)}>
              <span style={{ fontWeight: 'bold', color: '#6f42c1', fontSize: 18 }}>🕸️</span>
            </IconButton>
          </Tooltip>
          <Tooltip title={isPreview ? 'Edit' : 'Preview'}>
            <IconButton onClick={() => setIsPreview(!isPreview)}>
              {isPreview ? <EditIcon /> : <VisibilityIcon />}
            </IconButton>
          </Tooltip>
          <input
            type="file"
            ref={fileInputRef}
            style={{ display: 'none' }}
            accept="image/*"
            onChange={handleImageUpload}
          />
        </Box>
        <Slate editor={editor} initialValue={value} onChange={handleSlateChange}>
          <Editable
            style={{
              width: '100%',
              height: '100%',
              flexGrow: 1,
              resize: 'none',
              border: 'none',
              outline: 'none',
              background: 'transparent',
              color: 'inherit',
              fontSize: '1.1rem',
              lineHeight: 1.4,
              fontFamily: 'Inter, sans-serif',
              padding: 0,
              margin: 0,
              boxSizing: 'border-box',
            }}
            renderLeaf={props => <Leaf {...props} />}
            renderElement={props => <Element {...props} />}
            placeholder="Start writing your note..."
            spellCheck
            autoFocus
            onKeyDown={handleKeyDown}
          />
        </Slate>
        <DrawingBoard
          open={showDrawing}
          onClose={() => setShowDrawing(false)}
          drawingData={activeNote?.drawingData}
          onSave={handleDrawingSave}
        />
      </Box>
    </Box>
  );
};

const Leaf = ({ attributes, children, leaf }: any) => {
  if (leaf.bold) children = <strong>{children}</strong>;
  if (leaf.italic) children = <em style={{ background: '#ffeeba', color: '#d9534f', fontStyle: 'italic', border: '1px dashed #d9534f', padding: '0 2px' }}>{children}</em>; // DEBUG: highlight italics
  if (leaf.underline) children = <u>{children}</u>;
  return <span {...attributes}>{children}</span>;
};

// Helper to extract plain text from Slate children
function getTextFromChildren(children: any): string {
  if (typeof children === 'string') return children;
  if (Array.isArray(children)) return children.map(getTextFromChildren).join('');
  if (children && typeof children === 'object' && 'props' in children) return getTextFromChildren(children.props.children);
  return '';
}

const Element = ({ attributes, children, element }: any) => {
  switch (element.type) {
    case 'bulleted-list':
      return <ul {...attributes} style={{ margin: 0, paddingLeft: 24 }}>{children}</ul>;
    case 'numbered-list':
      return <ol {...attributes} style={{ margin: 0, paddingLeft: 24 }}>{children}</ol>;
    case 'list-item':
      return <li {...attributes} style={{ margin: 0 }}>{children}</li>;
    case 'code':
      return (
        <div {...attributes} style={{ 
          background: '#1e1e1e', 
          borderRadius: 6, 
          padding: 12, 
          overflow: 'auto', 
          margin: '8px 0',
          fontFamily: '"Fira Code", "Consolas", monospace',
        }}>
          <code style={{ 
            color: '#f8f8f2',
            fontSize: '0.95rem',
            lineHeight: 1.5,
          }}>
            {children}
          </code>
        </div>
      );
    case 'image':
      return (
        <div {...attributes} style={{ margin: '8px 0' }}>
          <img 
            src={element.url} 
            alt="" 
            style={{ 
              maxWidth: '100%', 
              height: 'auto',
              borderRadius: 4
            }} 
          />
          {children}
        </div>
      );
    default:
      return <p {...attributes} style={{ margin: '8px 0', lineHeight: 1.6 }}>{children}</p>;
  }
};

export default NoteEditor;
