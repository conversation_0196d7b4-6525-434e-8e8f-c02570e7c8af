import React, { useRef, useEffect, useState } from 'react';
import { Box, IconButton, Slider, Tooltip, Dialog, DialogContent } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ColorLensIcon from '@mui/icons-material/ColorLens';
import BrushIcon from '@mui/icons-material/Brush';
import StraightenIcon from '@mui/icons-material/Straighten';
import DeleteIcon from '@mui/icons-material/Delete';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';

interface Stroke {
  points: { x: number; y: number }[];
  color: string;
  thickness: number;
  type: 'freehand' | 'line';
}

interface DrawingBoardProps {
  open: boolean;
  onClose: () => void;
  drawingData?: Stroke[];
  onSave: (data: Stroke[]) => void;
}

const COLORS = ['#222', '#e53935', '#43a047', '#1e88e5', '#fbc02d', '#fff'];

const DrawingBoard: React.FC<DrawingBoardProps> = ({ open, onClose, drawingData, onSave }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [drawing, setDrawing] = useState<Stroke[]>(drawingData || []);
  const [currentStroke, setCurrentStroke] = useState<Stroke | null>(null);
  const [color, setColor] = useState('#222');
  const [thickness, setThickness] = useState(3);
  const [tool, setTool] = useState<'freehand' | 'line' | 'eraser'>('freehand');
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);
  const [eraserSize, setEraserSize] = useState(20);
  const [mousePos, setMousePos] = useState<{ x: number; y: number } | null>(null);

  useEffect(() => {
    if (open && drawingData) setDrawing(drawingData);
  }, [open, drawingData]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw all strokes
    drawing.forEach(stroke => {
      ctx.strokeStyle = stroke.color;
      ctx.lineWidth = stroke.thickness;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      ctx.beginPath();
      stroke.points.forEach((pt, i) => {
        if (i === 0) ctx.moveTo(pt.x, pt.y);
        else ctx.lineTo(pt.x, pt.y);
      });
      ctx.stroke();
    });

    // Draw current stroke
    if (currentStroke) {
      ctx.strokeStyle = currentStroke.color;
      ctx.lineWidth = currentStroke.thickness;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      ctx.beginPath();
      currentStroke.points.forEach((pt, i) => {
        if (i === 0) ctx.moveTo(pt.x, pt.y);
        else ctx.lineTo(pt.x, pt.y);
      });
      ctx.stroke();
    }

    // Draw eraser preview
    if (tool === 'eraser' && mousePos) {
      ctx.strokeStyle = '#ff6b6b';
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]);
      ctx.beginPath();
      ctx.arc(mousePos.x, mousePos.y, eraserSize / 2, 0, 2 * Math.PI);
      ctx.stroke();
      ctx.setLineDash([]);
    }
  }, [drawing, currentStroke, tool, mousePos, eraserSize]);

  const getCanvasPos = (e: React.MouseEvent | React.TouchEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };
    const rect = canvas.getBoundingClientRect();
    if ('touches' in e) {
      return {
        x: e.touches[0].clientX - rect.left,
        y: e.touches[0].clientY - rect.top,
      };
    } else {
      return {
        x: (e as React.MouseEvent).clientX - rect.left,
        y: (e as React.MouseEvent).clientY - rect.top,
      };
    }
  };

  const eraseStroke = (pos: { x: number; y: number }) => {
    setDrawing(drawing => drawing.filter(stroke => {
      // Check if any point in the stroke is within the eraser radius
      return !stroke.points.some(pt => {
        const distance = Math.hypot(pt.x - pos.x, pt.y - pos.y);
        return distance < eraserSize / 2;
      });
    }));
  };

  const handlePointerDown = (e: React.MouseEvent | React.TouchEvent) => {
    const pos = getCanvasPos(e);
    if (tool === 'eraser') {
      eraseStroke(pos);
      // Create a dummy stroke to track erasing state
      setCurrentStroke({ points: [pos], color: 'transparent', thickness: 0, type: 'freehand' });
      return;
    }
    if (tool === 'freehand') {
      setCurrentStroke({ points: [pos], color, thickness, type: 'freehand' });
    } else if (tool === 'line') {
      setStartPoint(pos);
      setCurrentStroke({ points: [pos, pos], color, thickness, type: 'line' });
    }
  };

  const handlePointerMove = (e: React.MouseEvent | React.TouchEvent) => {
    const pos = getCanvasPos(e);
    setMousePos(pos);

    if (tool === 'eraser') {
      if (currentStroke) { // Only erase when mouse is down
        eraseStroke(pos);
      }
      return;
    }
    if (!currentStroke) return;
    if (tool === 'freehand') {
      setCurrentStroke({ ...currentStroke, points: [...currentStroke.points, pos] });
    } else if (tool === 'line' && startPoint) {
      setCurrentStroke({ ...currentStroke, points: [startPoint, pos] });
    }
  };

  const handlePointerUp = () => {
    if (currentStroke && tool !== 'eraser') {
      setDrawing([...drawing, currentStroke]);
    }
    setCurrentStroke(null);
    setStartPoint(null);
  };

  const handleClear = () => setDrawing([]);

  return (
    <Dialog open={open} onClose={onClose} fullScreen PaperProps={{ sx: { bgcolor: '#fff' } }}>
      <DialogContent sx={{ p: 0, position: 'relative', height: '100vh', width: '100vw', overflow: 'hidden' }}>
        <Box sx={{ position: 'absolute', top: 16, left: 16, zIndex: 10, display: 'flex', gap: 1, alignItems: 'center', bgcolor: 'rgba(255,255,255,0.8)', borderRadius: 2, p: 1 }}>
          <Tooltip title="Pen">
            <IconButton color={tool === 'freehand' ? 'primary' : 'default'} onClick={() => setTool('freehand')}><BrushIcon /></IconButton>
          </Tooltip>
          <Tooltip title="Ruler (Straight Line)">
            <IconButton color={tool === 'line' ? 'primary' : 'default'} onClick={() => setTool('line')}><StraightenIcon /></IconButton>
          </Tooltip>
          <Tooltip title="Eraser">
            <IconButton color={tool === 'eraser' ? 'primary' : 'default'} onClick={() => setTool('eraser')}><AutoFixHighIcon /></IconButton>
          </Tooltip>
          <Tooltip title="Color">
            <IconButton><ColorLensIcon /></IconButton>
          </Tooltip>
          {COLORS.map(c => (
            <Box key={c} onClick={() => setColor(c)} sx={{ width: 24, height: 24, bgcolor: c, border: color === c ? '2px solid #333' : '1px solid #aaa', borderRadius: '50%', cursor: 'pointer', m: 0.5 }} />
          ))}
          {tool === 'eraser' ? (
            <Slider
              min={10}
              max={50}
              value={eraserSize}
              onChange={(_, v) => setEraserSize(v as number)}
              sx={{ width: 80, mx: 2 }}
              valueLabelDisplay="auto"
              valueLabelFormat={(value) => `${value}px`}
            />
          ) : (
            <Slider
              min={1}
              max={12}
              value={thickness}
              onChange={(_, v) => setThickness(v as number)}
              sx={{ width: 80, mx: 2 }}
              valueLabelDisplay="auto"
              valueLabelFormat={(value) => `${value}px`}
            />
          )}
          <Tooltip title="Clear">
            <IconButton onClick={handleClear}>🧹</IconButton>
          </Tooltip>
        </Box>
        <Box sx={{ position: 'absolute', top: 16, right: 16, zIndex: 10 }}>
          <Tooltip title="Close">
            <IconButton onClick={onClose}><CloseIcon /></IconButton>
          </Tooltip>
          <Tooltip title="Save">
            <IconButton onClick={() => onSave(drawing)} color="primary">💾</IconButton>
          </Tooltip>
        </Box>
        <canvas
          ref={canvasRef}
          width={window.innerWidth}
          height={window.innerHeight}
          style={{
            width: '100vw',
            height: '100vh',
            touchAction: 'none',
            background: '#fff',
            display: 'block',
            cursor: tool === 'line' ? 'crosshair' : tool === 'eraser' ? 'none' : 'pointer'
          }}
          onMouseDown={handlePointerDown}
          onMouseMove={handlePointerMove}
          onMouseUp={handlePointerUp}
          onMouseLeave={() => {
            handlePointerUp();
            setMousePos(null);
          }}
          onTouchStart={handlePointerDown}
          onTouchMove={handlePointerMove}
          onTouchEnd={handlePointerUp}
        />
      </DialogContent>
    </Dialog>
  );
};

export default DrawingBoard; 