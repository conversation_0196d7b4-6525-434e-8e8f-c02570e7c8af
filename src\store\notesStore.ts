import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface Note {
  id: string;
  title: string;
  content: string;
  lastModified: Date;
  drawingData?: any[]; // Array of strokes for the drawing board
}

interface NotesStore {
  notes: Note[];
  activeNoteId: string | null;
  addNote: (note: Omit<Note, 'id' | 'lastModified'>) => void;
  updateNote: (id: string, updates: Partial<Note>) => void;
  deleteNote: (id: string) => void;
  setActiveNote: (id: string | null) => void;
}

export const useNotesStore = create<NotesStore>()(
  persist(
    (set) => ({
      notes: [],
      activeNoteId: null,
      addNote: (note) =>
        set((state) => ({
          notes: [
            {
              ...note,
              id: Date.now().toString(),
              lastModified: new Date(),
            },
            ...state.notes,
          ],
        })),
      updateNote: (id, updates) =>
        set((state) => ({
          notes: state.notes.map((note) =>
            note.id === id
              ? { ...note, ...updates, lastModified: new Date() }
              : note
          ),
        })),
      deleteNote: (id) =>
        set((state) => ({
          notes: state.notes.filter((note) => note.id !== id),
          activeNoteId: state.activeNoteId === id ? null : state.activeNoteId,
        })),
      setActiveNote: (id) => set({ activeNoteId: id }),
    }),
    {
      name: 'notes-storage',
    }
  )
); 