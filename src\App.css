#root {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  .logo {
    animation: none;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.app-title {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--text-primary);
  margin: 0;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.editor-container {
  height: calc(100vh - 64px);
  overflow: hidden;
}
