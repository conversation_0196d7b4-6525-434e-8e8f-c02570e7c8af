import { useState } from 'react'
import { ThemeProvider, createTheme, CssBaseline } from '@mui/material'
import { Box, IconButton, Typography } from '@mui/material'
import Brightness4Icon from '@mui/icons-material/Brightness4'
import Brightness7Icon from '@mui/icons-material/Brightness7'
import Sidebar from './components/Sidebar'
import Editor from './components/Editor'
import MenuIcon from '@mui/icons-material/Menu'
import './App.css'

function App() {
  const [darkMode, setDarkMode] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const theme = createTheme({
    palette: {
      mode: darkMode ? 'dark' : 'light',
      background: {
        default: darkMode ? '#1a1a1a' : '#ffffff',
        paper: darkMode ? '#2d2d2d' : '#f5f5f5',
      },
    },
    typography: {
      fontFamily: 'Inter, sans-serif',
    },
  })

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ display: 'flex', height: '100vh' }}>
        {sidebarOpen && <Sidebar />}
        <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center',
            justifyContent: 'space-between',
            p: 2,
            borderBottom: 1,
            borderColor: 'divider',
            height: '64px'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <IconButton onClick={() => setSidebarOpen(!sidebarOpen)} color="inherit">
                <MenuIcon />
              </IconButton>
              <Typography variant="h5" className="app-title">
                Minimal Notes
              </Typography>
            </Box>
            <IconButton onClick={() => setDarkMode(!darkMode)} color="inherit">
              {darkMode ? <Brightness7Icon /> : <Brightness4Icon />}
            </IconButton>
          </Box>
          <Box className="editor-container">
            <Editor centerContent={!sidebarOpen} />
          </Box>
        </Box>
      </Box>
    </ThemeProvider>
  )
}

export default App
