import { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  IconButton,
  TextField,
  Drawer,
  Divider,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import BrushIcon from '@mui/icons-material/Brush';
import { useNotesStore } from '../store/notesStore';

const Sidebar = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const { notes, activeNoteId, addNote, setActiveNote } = useNotesStore();

  const filteredNotes = notes.filter(note =>
    note.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const createNewNote = () => {
    addNote({
      title: 'Untitled Note',
      content: '',
    });
  };

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: 280,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: 280,
          boxSizing: 'border-box',
        },
      }}
    >
      <Box sx={{ p: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
        <TextField
          size="small"
          placeholder="Search notes..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
          }}
          fullWidth
        />
        <IconButton onClick={createNewNote} color="primary">
          <AddIcon />
        </IconButton>
      </Box>
      <Divider />
      <List>
        {filteredNotes.map((note) => (
          <ListItem key={note.id} disablePadding>
            <ListItemButton
              selected={note.id === activeNoteId}
              onClick={() => setActiveNote(note.id)}
            >
              <ListItemText
                primary={<span>{note.title} {note.drawingData && note.drawingData.length > 0 && <BrushIcon fontSize="small" sx={{ verticalAlign: 'middle', ml: 1 }} />}</span>}
                secondary={new Date(note.lastModified).toLocaleDateString()}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Drawer>
  );
};

export default Sidebar; 