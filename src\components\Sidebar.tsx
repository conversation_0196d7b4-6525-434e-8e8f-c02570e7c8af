import { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  IconButton,
  TextField,
  Drawer,
  Divider,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import BrushIcon from '@mui/icons-material/Brush';
import TextFieldsIcon from '@mui/icons-material/TextFields';
import BlendIcon from '@mui/icons-material/Blend';
import { useNotesStore } from '../store/notesStore';

const Sidebar = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const { notes, activeNoteId, addNote, setActiveNote } = useNotesStore();

  const filteredNotes = notes.filter(note =>
    note.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const createNewNote = () => {
    addNote({
      title: 'Untitled Note',
      content: '',
      type: 'text',
    });
  };

  const getTypeIcon = (note: any) => {
    switch (note.type) {
      case 'drawing':
        return <BrushIcon fontSize="small" sx={{ verticalAlign: 'middle', ml: 1, color: '#ff6b6b' }} />;
      case 'mixed':
        return <BlendIcon fontSize="small" sx={{ verticalAlign: 'middle', ml: 1, color: '#4ecdc4' }} />;
      default:
        return <TextFieldsIcon fontSize="small" sx={{ verticalAlign: 'middle', ml: 1, color: '#45b7d1' }} />;
    }
  };

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: 280,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: 280,
          boxSizing: 'border-box',
        },
      }}
    >
      <Box sx={{ p: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
        <TextField
          size="small"
          placeholder="Search notes..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
          }}
          fullWidth
        />
        <IconButton onClick={createNewNote} color="primary">
          <AddIcon />
        </IconButton>
      </Box>
      <Divider />
      <List>
        {filteredNotes.map((note) => (
          <ListItem key={note.id} disablePadding>
            <ListItemButton
              selected={note.id === activeNoteId}
              onClick={() => setActiveNote(note.id)}
            >
              <ListItemText
                primary={
                  <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span>{note.title}</span>
                    {getTypeIcon(note)}
                  </span>
                }
                secondary={new Date(note.lastModified).toLocaleDateString()}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Drawer>
  );
};

export default Sidebar; 